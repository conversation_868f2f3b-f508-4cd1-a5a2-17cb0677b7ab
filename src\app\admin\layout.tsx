import type { Metadata } from 'next';
import type React from 'react';

export const metadata: Metadata = {
  title: 'Admin Panel - Portfolio Management',
  description: 'Admin panel for managing portfolio content',
  robots: {
    index: false,
    follow: false,
  },
};

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return children;
}
