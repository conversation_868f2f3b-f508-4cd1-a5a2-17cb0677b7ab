'use client';

import { motion } from 'framer-motion';
import { FolderOpen, Mail, Code, Star, Plus, ArrowRight, Users } from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

import AdminProtectedLayout from '@/components/admin/AdminProtectedLayout';
import ErrorMessage from '@/components/admin/ErrorMessage';
import LoadingSpinner from '@/components/admin/LoadingSpinner';
import { ApiService } from '@/lib/api-service';

interface DashboardStats {
  totalProjects: number;
  featuredProjects: number;
  unreadMessages: number;
  totalSkills: number;
  completedProjects: number;
  inProgressProjects: number;
}

interface RecentMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  createdAt: string;
  read: boolean;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    featuredProjects: 0,
    unreadMessages: 0,
    totalSkills: 0,
    completedProjects: 0,
    inProgressProjects: 0,
  });
  const [recentMessages, setRecentMessages] = useState<RecentMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setError('');

      // Fetch portfolio data and contact messages using centralized API
      const [portfolioResponse, contactsResponse] = await Promise.all([
        ApiService.getAdminPortfolio(),
        ApiService.getContactMessages(),
      ]);

      if (portfolioResponse.success && portfolioResponse.data) {
        const portfolioData = portfolioResponse.data;
        const projects = portfolioData.projects || [];
        const skills = portfolioData.skills || [];

        const messages = contactsResponse.success && contactsResponse.data
          ? contactsResponse.data.messages || []
          : [];

        setStats({
          totalProjects: projects.length,
          featuredProjects: projects.filter((p: any) => p.featured).length,
          completedProjects: projects.filter((p: any) => p.status === 'completed').length,
          inProgressProjects: projects.filter((p: any) => p.status === 'in-progress').length,
          totalSkills: skills.length,
          unreadMessages: messages.filter((m: any) => !m.read).length,
        });

        // Get recent messages (last 5)
        setRecentMessages(
          messages
            .sort(
              (a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
            )
            .slice(0, 5),
        );
      } else {
        // Handle API errors
        const errorMsg = portfolioResponse.error || contactsResponse.error || 'Failed to fetch data';
        setError(errorMsg);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      name: 'Add New Project',
      description: 'Create a new portfolio project',
      href: '/admin/projects/new',
      icon: Plus,
      color: 'blue',
    },
    {
      name: 'View Messages',
      description: 'Check contact form submissions',
      href: '/admin/contacts',
      icon: Mail,
      color: 'green',
    },
    {
      name: 'Edit About',
      description: 'Update your profile information',
      href: '/admin/about',
      icon: Users,
      color: 'purple',
    },
    {
      name: 'Manage Projects',
      description: 'View and edit all projects',
      href: '/admin/projects',
      icon: FolderOpen,
      color: 'orange',
    },
  ];

  const statCards = [
    {
      name: 'Total Projects',
      value: stats.totalProjects,
      icon: FolderOpen,
      color: 'blue',
      description: `${stats.featuredProjects} featured`,
    },
    {
      name: 'Unread Messages',
      value: stats.unreadMessages,
      icon: Mail,
      color: 'green',
      description: 'Contact form submissions',
    },
    {
      name: 'Completed Projects',
      value: stats.completedProjects,
      icon: Star,
      color: 'purple',
      description: `${stats.inProgressProjects} in progress`,
    },
    {
      name: 'Skills Listed',
      value: stats.totalSkills,
      icon: Code,
      color: 'orange',
      description: 'Technical skills',
    },
  ];

  return (
    <AdminProtectedLayout
      title="Dashboard"
      subtitle="Welcome back! Here's what's happening with your portfolio."
    >
      <div className="p-6 space-y-8">
        {error && (
          <ErrorMessage
            message={error}
            onRetry={fetchDashboardData}
            onDismiss={() => setError('')}
          />
        )}

        {loading ? (
          <LoadingSpinner size="lg" text="Loading dashboard..." className="py-12" />
        ) : (
          <>
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {statCards.map((stat, index) => (
                <motion.div
                  key={stat.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -4, scale: 1.02 }}
                  className="bg-surface-light dark:bg-surface-dark rounded-xl border border-border-light dark:border-border-dark p-6 shadow-sm hover:shadow-lg transition-all duration-200 group"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-text-secondary-light dark:text-text-secondary-dark group-hover:text-text-primary-light dark:group-hover:text-text-primary-dark transition-colors">
                        {stat.name}
                      </p>
                      <p className="text-3xl font-bold text-text-primary-light dark:text-text-primary-dark mt-2">
                        {stat.value}
                      </p>
                      <p className="text-xs text-text-muted-light dark:text-text-muted-dark mt-1">
                        {stat.description}
                      </p>
                    </div>
                    <div
                      className={`p-4 rounded-xl group-hover:scale-110 transition-transform duration-200 ${
                        stat.color === 'blue'
                          ? 'bg-blue-100 dark:bg-blue-900/20'
                          : stat.color === 'green'
                            ? 'bg-green-100 dark:bg-green-900/20'
                            : stat.color === 'purple'
                              ? 'bg-purple-100 dark:bg-purple-900/20'
                              : 'bg-orange-100 dark:bg-orange-900/20'
                      }`}
                    >
                      <stat.icon
                        className={`w-7 h-7 ${
                          stat.color === 'blue'
                            ? 'text-blue-600 dark:text-blue-400'
                            : stat.color === 'green'
                              ? 'text-green-600 dark:text-green-400'
                              : stat.color === 'purple'
                                ? 'text-purple-600 dark:text-purple-400'
                                : 'text-orange-600 dark:text-orange-400'
                        }`}
                      />
                    </div>
                  </div>
                  <div className="mt-4 h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${Math.min((stat.value / 10) * 100, 100)}%` }}
                      transition={{ delay: index * 0.1 + 0.5, duration: 0.8 }}
                      className={`h-full rounded-full ${
                        stat.color === 'blue'
                          ? 'bg-blue-500'
                          : stat.color === 'green'
                            ? 'bg-green-500'
                            : stat.color === 'purple'
                              ? 'bg-purple-500'
                              : 'bg-orange-500'
                      }`}
                    />
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-surface-light dark:bg-surface-dark rounded-xl border border-border-light dark:border-border-dark p-6 shadow-sm"
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-text-primary-light dark:text-text-primary-dark">
                    Quick Actions
                  </h3>
                  <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></div>
                </div>
                <div className="space-y-2">
                  {quickActions.map((action, index) => (
                    <motion.div
                      key={action.name}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      whileHover={{ x: 4 }}
                    >
                      <Link
                        href={action.href}
                        className="flex items-center p-4 rounded-xl border border-border-light dark:border-border-dark hover:bg-gray-50 dark:hover:bg-gray-700/30 hover:border-primary-200 dark:hover:border-primary-800 transition-all duration-200 group"
                      >
                        <div
                          className={`p-3 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-200 ${
                            action.color === 'blue'
                              ? 'bg-blue-100 dark:bg-blue-900/20'
                              : action.color === 'green'
                                ? 'bg-green-100 dark:bg-green-900/20'
                                : action.color === 'purple'
                                  ? 'bg-purple-100 dark:bg-purple-900/20'
                                  : 'bg-orange-100 dark:bg-orange-900/20'
                          }`}
                        >
                          <action.icon
                            className={`w-5 h-5 ${
                              action.color === 'blue'
                                ? 'text-blue-600 dark:text-blue-400'
                                : action.color === 'green'
                                  ? 'text-green-600 dark:text-green-400'
                                  : action.color === 'purple'
                                    ? 'text-purple-600 dark:text-purple-400'
                                    : 'text-orange-600 dark:text-orange-400'
                            }`}
                          />
                        </div>
                        <div className="flex-1">
                          <p className="font-semibold text-text-primary-light dark:text-text-primary-dark group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            {action.name}
                          </p>
                          <p className="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                            {action.description}
                          </p>
                        </div>
                        <ArrowRight className="w-5 h-5 text-text-muted-light dark:text-text-muted-dark group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-200" />
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Recent Messages */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-surface-light dark:bg-surface-dark rounded-xl border border-border-light dark:border-border-dark p-6 shadow-sm"
              >
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-bold text-text-primary-light dark:text-text-primary-dark">
                      Recent Messages
                    </h3>
                    {recentMessages.filter(m => !m.read).length > 0 && (
                      <span className="px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 text-xs font-medium rounded-full">
                        {recentMessages.filter(m => !m.read).length} new
                      </span>
                    )}
                  </div>
                  <Link
                    href="/admin/contacts"
                    className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors"
                  >
                    View all →
                  </Link>
                </div>
                <div className="space-y-2">
                  {recentMessages.length === 0 ? (
                    <div className="text-center py-8">
                      <Mail className="w-12 h-12 text-text-muted-light dark:text-text-muted-dark mx-auto mb-3" />
                      <p className="text-text-secondary-light dark:text-text-secondary-dark text-sm">
                        No messages yet
                      </p>
                    </div>
                  ) : (
                    recentMessages.map((message, index) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 + index * 0.1 }}
                        className={`p-4 rounded-xl border transition-all duration-200 hover:shadow-md ${
                          message.read
                            ? 'border-border-light dark:border-border-dark bg-gray-50/50 dark:bg-gray-800/30'
                            : 'border-primary-200 dark:border-primary-800 bg-primary-50/50 dark:bg-primary-900/10 shadow-sm'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <p className="font-semibold text-text-primary-light dark:text-text-primary-dark text-sm">
                                {message.name}
                              </p>
                              {!message.read && (
                                <span className="w-2 h-2 bg-primary-500 rounded-full animate-pulse"></span>
                              )}
                            </div>
                            <p className="text-sm text-text-secondary-light dark:text-text-secondary-dark truncate mb-2">
                              {message.subject}
                            </p>
                            <div className="flex items-center space-x-2">
                              <p className="text-xs text-text-muted-light dark:text-text-muted-dark">
                                {new Date(message.createdAt).toLocaleDateString()}
                              </p>
                              <span className="text-xs text-text-muted-light dark:text-text-muted-dark">
                                •
                              </span>
                              <p className="text-xs text-text-muted-light dark:text-text-muted-dark">
                                {message.email}
                              </p>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  )}
                </div>
              </motion.div>
            </div>
          </>
        )}
      </div>
    </AdminProtectedLayout>
  );
}
