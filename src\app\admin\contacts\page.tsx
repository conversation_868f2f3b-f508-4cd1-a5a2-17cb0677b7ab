'use client';

import { motion } from 'framer-motion';
import { Mail, Calendar, User, MessageSquare, Trash2, Eye, EyeOff, Reply } from 'lucide-react';
import { useState, useEffect } from 'react';

import AdminProtectedLayout from '@/components/admin/AdminProtectedLayout';
import ErrorMessage from '@/components/admin/ErrorMessage';
import LoadingSpinner from '@/components/admin/LoadingSpinner';
import { ApiService } from '@/lib/api-service';

interface ContactMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  createdAt: string;
  read: boolean;
}

export default function ContactsManagement() {
  const [messages, setMessages] = useState<ContactMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');

  useEffect(() => {
    fetchMessages();
  }, []);

  const fetchMessages = async () => {
    try {
      const response = await ApiService.getContactMessages();
      if (response.success && response.data) {
        setMessages(response.data.messages || []);
      } else {
        setError(response.error || 'Failed to fetch messages');
      }
    } catch (error) {
      console.error('Fetch messages error:', error);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      const response = await ApiService.markMessageAsRead(id);

      if (response.success) {
        setMessages(messages.map(msg => (msg.id === id ? { ...msg, read: true } : msg)));
      } else {
        console.error('Failed to mark as read:', response.error);
      }
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  };

  const deleteMessage = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this message?')) {
      return;
    }

    try {
      const response = await ApiService.deleteContactMessage(id);

      if (response.success) {
        setMessages(messages.filter(msg => msg.id !== id));
      } else {
        setError(response.error || 'Failed to delete message');
      }
    } catch (error) {
      console.error('Delete message error:', error);
      setError('Network error. Please try again.');
    }
  };

  const filteredMessages = messages.filter(msg => {
    if (filter === 'unread') {
      return !msg.read;
    }
    if (filter === 'read') {
      return msg.read;
    }
    return true;
  });

  const unreadCount = messages.filter(msg => !msg.read).length;

  return (
    <AdminProtectedLayout
      title="Contact Messages"
      subtitle={`Manage messages from your contact form${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
    >
      <div className="p-6">
        {error && (
          <ErrorMessage
            message={error}
            onRetry={fetchMessages}
            onDismiss={() => setError('')}
            className="mb-6"
          />
        )}

        {loading ? (
          <LoadingSpinner size="lg" text="Loading messages..." className="py-12" />
        ) : (
          <>
            {/* Filter Tabs */}
            <div className="mb-8">
              <div className="flex space-x-2 bg-surface-light dark:bg-surface-dark p-2 rounded-xl border border-border-light dark:border-border-dark w-fit">
                {[
                  {
                    key: 'all',
                    label: 'All Messages',
                    count: messages.length,
                    icon: MessageSquare,
                  },
                  { key: 'unread', label: 'Unread', count: unreadCount, icon: Eye },
                  {
                    key: 'read',
                    label: 'Read',
                    count: messages.length - unreadCount,
                    icon: EyeOff,
                  },
                ].map(tab => (
                  <motion.button
                    key={tab.key}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setFilter(tab.key as any)}
                    className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 ${
                      filter === tab.key
                        ? 'bg-primary-500 text-white shadow-lg'
                        : 'text-text-secondary-light dark:text-text-secondary-dark hover:text-text-primary-light dark:hover:text-text-primary-dark hover:bg-gray-100 dark:hover:bg-gray-700/50'
                    }`}
                  >
                    <tab.icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                    <span
                      className={`px-2 py-0.5 rounded-full text-xs font-bold ${
                        filter === tab.key
                          ? 'bg-white/20 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-text-muted-light dark:text-text-muted-dark'
                      }`}
                    >
                      {tab.count}
                    </span>
                  </motion.button>
                ))}
              </div>
            </div>

            {filteredMessages.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-16"
              >
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900/20 dark:to-primary-800/20 rounded-2xl flex items-center justify-center">
                  <Mail className="w-10 h-10 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-xl font-bold text-text-primary-light dark:text-text-primary-dark mb-3">
                  {filter === 'all' ? 'No messages yet' : `No ${filter} messages`}
                </h3>
                <p className="text-text-secondary-light dark:text-text-secondary-dark max-w-md mx-auto">
                  {filter === 'all'
                    ? 'Messages from your contact form will appear here when visitors reach out.'
                    : `You have no ${filter} messages at the moment.`}
                </p>
              </motion.div>
            ) : (
              <div className="space-y-4">
                {filteredMessages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`bg-white dark:bg-gray-800 rounded-xl border-2 transition-all hover:shadow-lg ${
                      message.read
                        ? 'border-gray-200 dark:border-gray-700'
                        : 'border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/10'
                    }`}
                  >
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start space-x-4">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              message.read
                                ? 'bg-gray-100 dark:bg-gray-700'
                                : 'bg-blue-100 dark:bg-blue-900/20'
                            }`}
                          >
                            <User
                              className={`w-5 h-5 ${
                                message.read
                                  ? 'text-gray-600 dark:text-gray-400'
                                  : 'text-blue-600 dark:text-blue-400'
                              }`}
                            />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {message.name}
                              </h3>
                              {!message.read && (
                                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                              {message.email}
                            </p>
                            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                              <Calendar className="w-3 h-3 mr-1" />
                              {new Date(message.createdAt).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              })}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {!message.read && (
                            <button
                              onClick={() => markAsRead(message.id)}
                              className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                              title="Mark as read"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                          )}
                          <a
                            href={`mailto:${message.email}?subject=Re: ${message.subject}`}
                            className="p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                            title="Reply via email"
                          >
                            <Reply className="w-4 h-4" />
                          </a>
                          <button
                            onClick={() => deleteMessage(message.id)}
                            className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                            title="Delete message"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                          <MessageSquare className="w-4 h-4 mr-2" />
                          {message.subject}
                        </h4>
                        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                          <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                            {message.message}
                          </p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </AdminProtectedLayout>
  );
}
